import { useState, useEffect } from "react";

const Carousel = ({ slides, autoPlay = true, interval = 3000 }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    if (!slides.length) return;
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? slides.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    if (!slides.length) return;
    const isLastSlide = currentIndex === slides.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  const goToSlide = (slideIndex) => {
    setCurrentIndex(slideIndex);
  };

  useEffect(() => {
    if (!autoPlay || !slides.length) return;

    const timer = setTimeout(() => {
      goToNext();
    }, interval);

    return () => clearTimeout(timer);
  }, [currentIndex, autoPlay, interval, slides.length]);

  if (!slides || !slides.length) {
    return (
      <div className="h-96 w-full flex items-center justify-center bg-gray-100 rounded-lg">
        <span className="text-gray-400 text-xl">No slides available</span>
      </div>
    );
  }

  return (
    <div className="h-96 w-full ">
      <div className="group relative h-full w-full">
        {/* Left Arrow - Shows on hover */}
        <button
          onClick={goToPrevious}
          className="absolute left-4 top-1/2 z-10 -translate-y-1/2 transform rounded-full bg-black/30 p-2 text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100"
        >
          ❮
        </button>

        {/* Slides */}
        <div className="h-full w-full overflow-hidden rounded-lg">
          <div
            className="h-full w-full bg-cover bg-center transition-all duration-500 ease-in-out"
            style={{
              backgroundImage: `url(${
                slides[currentIndex].image || slides[currentIndex].url || ""
              })`,
            }}
          >
            <div className="flex h-full items-end pb-12">
              <div className="w-full p-4 text-white">
                <h2 className="text-xl font-bold">
                  {slides[currentIndex].title || ""}
                </h2>
              </div>
            </div>
          </div>
        </div>

        {/* Right Arrow - Shows on hover */}
        <button
          onClick={goToNext}
          className="absolute right-4 top-1/2 z-10 -translate-y-1/2 transform rounded-full bg-black/30 p-2 text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100"
        >
          ❯
        </button>

        {/* Dots Indicator */}
        <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 transform space-x-2">
          {slides.map((slide, slideIndex) => (
            <button
              key={slideIndex}
              onClick={() => goToSlide(slideIndex)}
              className={`h-3 w-3 rounded-full transition-all ${
                slideIndex === currentIndex ? "bg-white" : "bg-white/50"
              }`}
            ></button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Carousel;
