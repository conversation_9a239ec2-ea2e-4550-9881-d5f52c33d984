import Toast from "../Toast";
import { faEdit, faSave } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { faPlusCircle, faTrash } from "@fortawesome/free-solid-svg-icons";

import { faCheckCircle } from "@fortawesome/free-solid-svg-icons";
import { useState } from "react";

export default function MyAddress() {
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    label: "",
    address: "",
  });
  const [addresses, setAddresses] = useState([
    {
      label: "Home",
      address: "2972 Westheimer Rd. Santa Ana, Illinois 85486",
    },
    {
      label: "My Grandparent's House",
      address: "2972 Westheimer Rd. Santa Ana, Illinois 85486",
    },
    {
      label: "Office",
      address: "2972 Westheimer Rd. Santa Ana, Illinois 85486",
    },
  ]);
  const [toast, setToast] = useState({
    show: false,
    message: "",
    type: "success",
  });
  const [editingIdx, setEditingIdx] = useState(null);
  const [editForm, setEditForm] = useState({ label: "", address: "" });
  const [confirmDeleteIdx, setConfirmDeleteIdx] = useState(null);
  const [selectedIdx, setSelectedIdx] = useState(0); // Địa chỉ được chọn mặc định là 0
  // Chọn địa chỉ nhận hàng
  const handleSelect = (idx) => {
    setSelectedIdx(idx);
    // Lưu địa chỉ được chọn vào localStorage
    const selectedAddress = addresses[idx];
    localStorage.setItem("selectedAddress", JSON.stringify(selectedAddress));
    setToast({
      show: true,
      message: "Đã chọn địa chỉ nhận hàng!",
      type: "success",
    });
  };

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setAddresses([...addresses, { label: form.label, address: form.address }]);
    setShowForm(false);
    setForm({ label: "", address: "" });
    setToast({
      show: true,
      message: "Thêm địa chỉ thành công!",
      type: "success",
    });
  };

  // Sửa địa chỉ
  const handleEdit = (idx) => {
    setEditingIdx(idx);
    setEditForm({ ...addresses[idx] });
  };

  const handleEditChange = (e) => {
    setEditForm({ ...editForm, [e.target.name]: e.target.value });
  };

  const handleEditSave = (idx) => {
    const newAddresses = addresses.map((item, i) =>
      i === idx ? { ...editForm } : item
    );
    setAddresses(newAddresses);
    setEditingIdx(null);
    setToast({
      show: true,
      message: "Cập nhật địa chỉ thành công!",
      type: "success",
    });
  };

  const handleEditCancel = () => {
    setEditingIdx(null);
  };

  // Xác nhận xóa
  const handleDelete = (idx) => {
    setConfirmDeleteIdx(idx);
  };

  const confirmDelete = () => {
    if (confirmDeleteIdx !== null) {
      const newAddresses = addresses.filter((_, i) => i !== confirmDeleteIdx);
      setAddresses(newAddresses);
      setToast({
        show: true,
        message: "Xóa địa chỉ thành công!",
        type: "success",
      });
      setConfirmDeleteIdx(null);
    }
  };

  const cancelDelete = () => {
    setConfirmDeleteIdx(null);
  };

  return (
    <div className="col-span-4">
      <Toast
        show={toast.show}
        message={toast.message}
        type={toast.type}
        onClose={() => setToast({ ...toast, show: false })}
      />
      <div className="justify-between flex flex-row gap-2 items-center">
        <h1 className="text-2xl  md:text-3xl font-semibold m-4">My Address</h1>
        <button
          className="flex flex-row gap-2 items-center"
          onClick={() => setShowForm(true)}
        >
          <FontAwesomeIcon
            icon={faPlusCircle}
            className="text-4xl text-green-500 cursor-pointer hover:text-green-600"
          />
          <span className="text-xl">Add New Address</span>
        </button>
      </div>

      {/* Form nhập địa chỉ mới */}
      {showForm && (
        <form
          onSubmit={handleSubmit}
          className="bg-gray-50 rounded-xl m-4 p-4 shadow-md flex flex-col gap-4"
        >
          <div>
            <label className="block font-semibold mb-1">Label</label>
            <input
              type="text"
              name="label"
              value={form.label}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-400"
              placeholder="e.g. Home, Office..."
              required
            />
          </div>
          <div>
            <label className="block font-semibold mb-1">Address</label>
            <input
              type="text"
              name="address"
              value={form.address}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-400"
              placeholder="Enter address"
              required
            />
          </div>
          <div className="flex gap-2">
            <button
              type="submit"
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
            >
              Save
            </button>
            <button
              type="button"
              onClick={() => setShowForm(false)}
              className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"
            >
              Cancel
            </button>
          </div>
        </form>
      )}

      {/* Danh sách địa chỉ */}
      {addresses.map((item, idx) => (
        <div
          key={idx}
          className={`bg-gray-50 rounded-xl m-4 p-4 shadow-md flex items-center justify-between border-2 transition-all ${
            selectedIdx === idx ? "border-green-500" : "border-transparent"
          }`}
        >
          {editingIdx === idx ? (
            <div className="flex flex-col gap-2 w-full">
              <input
                type="text"
                name="label"
                value={editForm.label}
                onChange={handleEditChange}
                className="border rounded px-2 py-1 mb-2"
                required
              />
              <input
                type="text"
                name="address"
                value={editForm.address}
                onChange={handleEditChange}
                className="border rounded px-2 py-1"
                required
              />
              <div className="flex gap-2 mt-2">
                <button
                  type="button"
                  onClick={() => handleEditSave(idx)}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-1 rounded"
                >
                  Lưu
                </button>
                <button
                  type="button"
                  onClick={handleEditCancel}
                  className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-1 rounded"
                >
                  Hủy
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex flex-col gap-2">
                <p className="font-semibold text-xl flex items-center gap-2">
                  {item.label}
                  {selectedIdx === idx && (
                    <FontAwesomeIcon
                      icon={faCheckCircle}
                      className="text-green-500 text-xl"
                      title="Địa chỉ đang chọn"
                    />
                  )}
                </p>
                <p className="text-lg">{item.address}</p>
              </div>
              <div className="flex gap-4 items-center">
                <button
                  type="button"
                  onClick={() => handleSelect(idx)}
                  className={`px-2 py-1 rounded border ${
                    selectedIdx === idx
                      ? "bg-green-500 text-white border-green-500 cursor-default"
                      : "bg-white text-green-500 border-green-500 hover:bg-green-50"
                  }`}
                  disabled={selectedIdx === idx}
                  aria-label="Chọn địa chỉ này"
                >
                  {selectedIdx === idx ? "Đã chọn" : "Chọn"}
                </button>
                <button
                  type="button"
                  onClick={() => handleEdit(idx)}
                  className="text-green-500 hover:text-green-600 text-3xl"
                  aria-label="Sửa địa chỉ"
                >
                  <FontAwesomeIcon icon={faEdit} />
                </button>
                <button
                  type="button"
                  onClick={() => handleDelete(idx)}
                  className="ml-2 text-red-500 hover:text-red-700 text-3xl"
                  aria-label="Xóa địa chỉ"
                >
                  <FontAwesomeIcon icon={faTrash} />
                </button>
              </div>
            </>
          )}
        </div>
      ))}

      {/* Xác nhận xóa */}
      {confirmDeleteIdx !== null && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black opacity-90">
          <div className="bg-white rounded-lg shadow-lg p-6 min-w-[300px]">
            <p className="mb-4 text-lg">
              Bạn có chắc chắn muốn xóa địa chỉ này không?
            </p>
            <div className="flex gap-4 justify-end">
              <button
                onClick={confirmDelete}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
              >
                Đồng ý
              </button>
              <button
                onClick={cancelDelete}
                className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"
              >
                Hủy
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
