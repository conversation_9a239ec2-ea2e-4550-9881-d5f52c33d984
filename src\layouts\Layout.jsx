import React from "react";
import Hedea<PERSON> from "../components/Header";
import Footer from "../components/Footer";
import Navbar from "../components/Navbar";
import { Outlet } from "react-router";
import { useState, useEffect } from "react";
import axios from "axios";
export default function Layout() {
  const [categories, setCategories] = useState([]);
  useEffect(() => {
    let isMounted = true; // Flag để tránh memory leak

    const fetchCategories = async () => {
      try {
        const token = localStorage.getItem("token");
        const response = await axios.get(
          "http://localhost:3333/api/v1/category",
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        if (isMounted) {
          setCategories(response.data.data || []);
        }
      } catch (err) {
        if (!isMounted) return;

        console.error("Fetch error:", err);

        // Xử lý lỗi 401 (Unauthorized)
        if (err.response?.status === 401) {
          try {
            // Gọi API refresh token
            const refreshResponse = await axios.get(
              "http://localhost:3333/api/v1/auth/refresh-token"
            );
            if (isMounted) {
              localStorage.setItem("token", refreshResponse.data.data.token);
              fetchCategories();
            }
          } catch (err) {
            alert("Không thể làm mới token. Vui lòng đăng nhập lại.");
          }
        } else {
          alert("Lỗi khi tải dữ liệu. Vui lòng thử lại.");
        }
      }
    };

    fetchCategories();

    return () => {
      isMounted = false;
    }; // Cleanup khi component unmount
  }, []);
  return (
    <div>
      <Hedear />
      <Navbar list={categories} />
      <Outlet />
      <Footer />
    </div>
  );
}
