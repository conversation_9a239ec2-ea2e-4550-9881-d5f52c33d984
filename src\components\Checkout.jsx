import React, { useState, useEffect } from "react";
export default function Checkout() {
  const paymentMethods = [
    { value: "cod", label: "<PERSON>h toán khi nhận hàng (COD)" },
    { value: "bank", label: "Chuyển khoản ngân hàng" },
    { value: "card", label: "Thẻ tín dụng/Ghi nợ" },
  ];

  // <PERSON><PERSON><PERSON> lập tổng tiền đơn hàng (có thể truyền qua props nếu cần)
  const orderTotal = 697000;

  const [form, setForm] = useState({
    name: "",
    phone: "",
    address: "",
    payment: paymentMethods[0].value,
  });
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [success, setSuccess] = useState(false);
  const [coupon, setCoupon] = useState("");
  const [couponStatus, setCouponStatus] = useState("");
  const [discount, setDiscount] = useState(0);

  // L<PERSON>y địa chỉ nhận hàng đượ<PERSON> chọn từ localStorage
  useEffect(() => {
    const addr = localStorage.getItem("selectedAddress");
    if (addr) {
      const parsed = JSON.parse(addr);
      setSelectedAddress(parsed);
      setForm((prev) => ({ ...prev, address: parsed.address }));
    }
  }, []);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  // Xử lý nhập coupon
  const handleCouponChange = (e) => {
    setCoupon(e.target.value);
    setCouponStatus("");
  };

  // Áp dụng coupon
  const handleApplyCoupon = (e) => {
    e.preventDefault();
    // Ví dụ: chỉ chấp nhận mã SALE10 giảm 10%
    if (coupon.trim().toUpperCase() === "SALE10") {
      setDiscount(0.1); // 10%
      setCouponStatus("success");
    } else {
      setDiscount(0);
      setCouponStatus("fail");
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    setSuccess(true);
  };

  if (success)
    return (
      <div className="max-w-md mx-auto p-6 bg-white rounded-xl shadow mt-8 text-center">
        <h2 className="text-2xl font-bold mb-4 text-green-600">
          Đặt hàng thành công!
        </h2>
        <p>Cảm ơn bạn đã mua sắm tại cửa hàng của chúng tôi.</p>
      </div>
    );

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-xl shadow mt-8">
      <h1 className="text-2xl font-bold mb-6">Thông tin nhận hàng</h1>
      {selectedAddress && (
        <div className="mb-4 p-3 bg-green-50 border-l-4 border-green-400 rounded">
          <div className="font-semibold text-green-700 flex items-center gap-2">
            Địa chỉ đã chọn:
            <span className="font-bold">{selectedAddress.label}</span>
          </div>
          <div className="text-green-800">{selectedAddress.address}</div>
        </div>
      )}

      {/* Áp dụng coupon */}
      <form onSubmit={handleApplyCoupon} className="flex gap-2 mb-4">
        <input
          type="text"
          value={coupon}
          onChange={handleCouponChange}
          placeholder="Nhập mã giảm giá (VD: SALE10)"
          className="border rounded px-3 py-2 flex-1"
        />
        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded"
        >
          Áp dụng
        </button>
      </form>
      {couponStatus === "success" && (
        <div className="text-green-600 mb-2">
          Áp dụng mã giảm giá thành công! Đã giảm 10%.
        </div>
      )}
      {couponStatus === "fail" && (
        <div className="text-red-500 mb-2">Mã giảm giá không hợp lệ.</div>
      )}

      {/* Hiển thị tổng tiền */}
      <div className="mb-4 flex flex-col gap-1">
        <div className="flex justify-between">
          <span>Tạm tính:</span>
          <span>{orderTotal.toLocaleString()}₫</span>
        </div>
        {discount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Giảm giá:</span>
            <span>-{(orderTotal * discount).toLocaleString()}₫</span>
          </div>
        )}
        <div className="flex justify-between font-bold text-lg">
          <span>Thành tiền:</span>
          <span>{(orderTotal * (1 - discount)).toLocaleString()}₫</span>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <input
          type="text"
          name="name"
          value={form.name}
          onChange={handleChange}
          placeholder="Họ và tên"
          className="border rounded px-3 py-2"
          required
        />
        <input
          type="text"
          name="phone"
          value={form.phone}
          onChange={handleChange}
          placeholder="Số điện thoại"
          className="border rounded px-3 py-2"
          required
        />
        <input
          type="text"
          name="address"
          value={form.address}
          onChange={handleChange}
          placeholder="Địa chỉ nhận hàng"
          className="border rounded px-3 py-2"
          required
        />
        <div>
          <label className="font-semibold mb-1 block">
            Phương thức thanh toán
          </label>
          <select
            name="payment"
            value={form.payment}
            onChange={handleChange}
            className="border rounded px-3 py-2 w-full"
          >
            {paymentMethods.map((m) => (
              <option key={m.value} value={m.value}>
                {m.label}
              </option>
            ))}
          </select>
        </div>
        <button
          type="submit"
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded font-semibold text-lg"
        >
          Xác nhận đặt hàng
        </button>
      </form>
    </div>
  );
}
