import React, { useEffect, useState } from "react";

export default function Cart() {
  const [cart, setCart] = useState([]);

  useEffect(() => {
    const cartData = localStorage.getItem("cart");
    if (cartData) {
      setCart(JSON.parse(cartData));
    }
  }, []);

  const total = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);

  return (
    <div className="max-w-3xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Giỏ hàng</h1>
      {cart.length === 0 ? (
        <div>Giỏ hàng trống.</div>
      ) : (
        <div className="bg-white rounded-xl shadow-md p-6">
          {cart.map((item) => (
            <div
              key={item.id}
              className="flex items-center gap-4 border-b py-3 last:border-b-0"
            >
              <img
                src={item.image}
                alt={item.name}
                className="w-20 h-20 object-contain rounded"
              />
              <div className="flex-1">
                <div className="font-semibold">{item.name}</div>
                <div className="text-gray-500">
                  {item.price.toLocaleString()}₫ x {item.quantity}
                </div>
              </div>
              <div className="text-green-600 font-bold">
                {(item.price * item.quantity).toLocaleString()}₫
              </div>
            </div>
          ))}
          <div className="text-right mt-4 font-bold text-lg">
            Tổng cộng: {total.toLocaleString()}₫
          </div>
        </div>
      )}
    </div>
  );
}
