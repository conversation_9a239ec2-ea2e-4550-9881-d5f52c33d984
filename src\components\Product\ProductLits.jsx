import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCartPlus } from "@fortawesome/free-solid-svg-icons";
import { useNavigate } from "react-router;
import { useCart } from "../../context/CartContext";

export default function ProductLits({ listProduct }) {
  const navigate = useNavigate();
  const { cart, addToCart } = useCart();
  const handleAddToCart = (product) => {
    if (!product || !product.id) {
      alert("Sản phẩm không hợp lệ");
      return;
    }
    if (product.stock <= 0) {
      alert("Sản phẩm đã hết hàng!");
      return;
    }
    // Kiểm tra số lượng tồn kho nếu đã có trong giỏ
    const existing = cart.find((item) => item.id === product.id);
    if (existing && existing.quantity >= product.stock) {
      alert(`Bạn chỉ có thể mua tối đa ${product.stock} sản phẩm này`);
      return;
    }
    addToCart(product);
    alert(`Đã thêm ${product.name} vào giỏ hàng!`);
  };

  const handleViewDetail = (product) => {
    localStorage.setItem("productDetail", JSON.stringify(product));
    navigate(`/product/${product.id}`);
  };

  return (
    <div>
      <h1 className="text-2xl mb-3">Products</h1>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {listProduct.map((product) => (
          <div
            key={product.id}
            className="bg-white rounded-xl shadow-md p-3 flex flex-col items-center hover:scale-105 transition-transform duration-300 cursor-pointer"
            onClick={() => handleViewDetail(product)}
          >
            <img
              src={product.image}
              alt={product.name}
              className="w-48 h-48 object-contain mb-2"
            />
            <h3 className="text-sm font-semibold text-center mb-1">
              {product.name}
            </h3>
            <div className="text-red-500 text-xl mb-2">
              {product.price.toLocaleString()}₫
            </div>
            <div className="text-gray-600 text-sm mb-2">
              <span>Còn lại {product.stock} hàng</span>
            </div>
            <button
              className="bg-green-500 hover:bg-green-600 text-white p-2 rounded-full transition"
              onClick={(e) => {
                e.stopPropagation();
                handleAddToCart(product);
              }}
            >
              <FontAwesomeIcon icon={faCartPlus} /> Add to cart
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
