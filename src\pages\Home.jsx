import { useState, useEffect } from "react";
import Carousel from "../components/Carousel";
import Category from "../components/Category";
import axios from "axios";
import ProductLits from "../components/Product/ProductLits.jsx";
import Pagination from "../components/Pagination";
function Home() {
  const [slides, setSlides] = useState([]);
  const [categories, setCategories] = useState([]);
  const [products, setProducts] = useState([]);

  useEffect(() => {
    let isMounted = true; // Flag để tránh memory leak

    const fetchCategoriesAndProducts = async () => {
      try {
        const token = localStorage.getItem("token");

        // Gọi API song song
        const [categoryResponse, productResponse, slideResponse] =
          await Promise.all([
            axios.get("http://localhost:3333/api/v1/category", {
              headers: { Authorization: `Bearer ${token}` },
            }),
            axios.get("http://localhost:3333/api/v1/product", {
              headers: { Authorization: `Bear<PERSON> ${token}` },
            }),
            axios.get("http://localhost:3333/api/v1/slider", {
              headers: { Authorization: `Bearer ${token}` },
            }),
          ]);

        if (isMounted) {
          setCategories(categoryResponse.data.data || []);
          setProducts(productResponse.data.data || []);
          setSlides(slideResponse.data.data || []);
        }
      } catch (err) {
        if (!isMounted) return;

        console.error("Fetch error:", err);

        // Xử lý lỗi 401 (Unauthorized)
        if (err.response?.status === 401) {
          try {
            // Gọi API refresh token
            const refreshResponse = await axios.get(
              "http://localhost:3333/api/v1/auth/refresh-token"
            );

            if (refreshResponse.status === 200) {
              const newToken = refreshResponse.data.data.token;
              localStorage.setItem("token", newToken);

              // Thử gọi lại API sau khi có token mới
              const retryResponse = await axios.get(err.config.url, {
                headers: { Authorization: `Bearer ${newToken}` },
              });

              if (isMounted) {
                if (err.config.url.includes("categories")) {
                  setCategories(retryResponse.data.data || []);
                } else {
                  setProducts(retryResponse.data.data || []);
                }
              }
            } else {
              alert("Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại.");
            }
          } catch (refreshError) {
            alert("Không thể làm mới token. Vui lòng đăng nhập lại.");
          }
        } else {
          alert("Lỗi khi tải dữ liệu. Vui lòng thử lại.");
        }
      }
    };

    fetchCategoriesAndProducts();

    return () => {
      isMounted = false;
    }; // Cleanup khi component unmount
  }, []);

  // Hàm xử lý khi chọn category ở component Category
  const handleCategoryClick = async (categoryId) => {
    try {
      const token = localStorage.getItem("token");
      if (categoryId === "all") {
        // Lấy tất cả sản phẩm
        const res = await axios.get("http://localhost:3333/api/v1/product", {
          headers: { Authorization: `Bearer ${token}` },
        });
        setProducts(res.data.data || []);
      } else {
        // Lấy sản phẩm theo danh mục
        const res = await axios.get(
          `http://localhost:3333/api/v1/product/${categoryId}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        setProducts(res.data.data || []);
      }
    } catch (err) {
      alert("Không thể tải sản phẩm theo danh mục!");
    }
  };

  return (
    <div className="mx-auto max-w-7xl px-4 py-8  justify-center">
      <Carousel slides={slides} />
      <Category list={categories} onCategoryClick={handleCategoryClick} />
      <ProductLits listProduct={products} />
    </div>
  );
}

export default Home;
