import { useNavigate } from "react-router";
import { <PERSON> } from "react-router";
import ButtonArrow from "../components/ButtonArrow";
import axios from "axios";
import { useState } from "react";

import { decodeJWT } from "../utils/auth";
export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post(
        "http://localhost:3333/api/v1/auth/login",
        {
          email,
          password,
        }
      );
      if (response.data.statusCode === 201) {
        const { token } = response.data.data;
        localStorage.setItem("token", token);
        const decoded = decodeJWT(token);
        if (decoded) {
          localStorage.setItem(
            "user",
            JSON.stringify({
              id: decoded.id,
              username: decoded.username || decoded.name || "",
              email: decoded.email || "",
              phone: decoded.phone || "",
              address: decoded.address || "",
              role: decoded.role,
            })
          );
        }
        setError("");
        navigate("/");
      } else if (response.data.statusCode === 401) {
        setError(response.data.message);
      }
    } catch (err) {
      setError(err.response?.data?.message || "Đăng nhập thất bại");
    }
  };

  return (
    <div className="relative bg-[#29C16E] w-screen h-screen flex items-center justify-center">
      <ButtonArrow />
      <div className="bg-white flex flex-col w-100 min-w-96 shadow-md rounded-lg">
        <div className="flex justify-center mt-6">
          <Link
            to="/"
            className="text-2xl text-white bg-[#037E4B] px-2 py-4 rounded-2xl shadow-lg"
          >
            GROUP 01
          </Link>
        </div>
        <form
          onSubmit={handleSubmit}
          className="flex flex-col p-8 gap-1 w-full"
        >
          <h2 className="text-2xl font-bold text-[#29C16E] text-center">
            Welcome Back
          </h2>
          <div className="mt-4">
            <input
              type="text"
              placeholder="Enter your Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-4 py-2 border-2 border-[#29C16E] focus:outline-none rounded-xl"
              required
            />
          </div>
          <div className="mt-4">
            <input
              type="password"
              placeholder="Enter your Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-2 border-2 border-[#29C16E] focus:outline-none rounded-xl"
              required
            />
          </div>
          <div className={error ? "" : "hidden"}>
            <span className="text-red-500 px-1 py-2">{error}</span>
          </div>
          <div className="flex justify-between">
            <Link
              to="/register"
              className="text-sm text-[#29C16E] hover:underline hover:text-[#037E4B] px-1 py-2"
            >
              Register now ?
            </Link>
            <a
              href="#"
              className="text-sm text-[#29C16E] hover:underline hover:text-[#037E4B] px-1 py-2"
            >
              Forgot password ?
            </a>
          </div>
          <button
            type="submit"
            className="bg-[#29C16E] text-white py-2 rounded-xl hover:bg-[#037E4B]"
          >
            Login
          </button>
        </form>
        <div className="p-5 flex flex-row   gap-2 justify-center">
          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_119_2771)">
              <rect
                x="0.776123"
                y="0.464661"
                width="27.0417"
                height="27.0417"
                rx="13.5208"
                fill="white"
              />
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M14.297 0.464661C6.82895 0.464661 0.776123 6.51861 0.776123 13.9855C0.776123 21.4524 6.82895 27.5063 14.297 27.5063C21.7638 27.5063 27.8178 21.4524 27.8178 13.9855C27.8178 6.51861 21.7638 0.464661 14.297 0.464661ZM14.4547 21.8929C10.0965 21.8929 6.56755 18.355 6.56755 13.9855C6.56755 9.61601 10.0965 6.07806 14.4547 6.07806C16.5842 6.07806 18.3645 6.86339 19.7301 8.13886L17.5059 10.3675V10.363C16.6777 9.57207 15.6276 9.16644 14.4547 9.16644C11.8519 9.16644 9.73705 11.3703 9.73705 13.981C9.73705 16.5894 11.8519 18.8 14.4547 18.8C16.8163 18.8 18.4231 17.4457 18.7543 15.5866H14.4547V12.5027H21.8743C21.9734 13.0323 22.0264 13.5844 22.0264 14.1635C22.0264 18.6817 19.0101 21.8929 14.4547 21.8929Z"
                fill="#29C16E"
              />
            </g>
            <defs>
              <clipPath id="clip0_119_2771">
                <rect
                  x="0.776123"
                  y="0.464661"
                  width="27.0417"
                  height="27.0417"
                  rx="13.5208"
                  fill="white"
                />
              </clipPath>
            </defs>
          </svg>

          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_119_2775)">
              <rect
                x="0.479248"
                y="0.464661"
                width="27.0417"
                height="27.0417"
                rx="13.5208"
                fill="white"
              />
              <path
                d="M27.5209 14.0683C27.5209 6.55409 21.4669 0.462952 14 0.462952C6.52978 0.464642 0.47583 6.55409 0.47583 14.07C0.47583 20.8591 5.42107 26.4872 11.884 27.508V18.0012H8.45312V14.07H11.8874V11.07C11.8874 7.66111 13.9071 5.77833 16.9949 5.77833C18.4754 5.77833 20.0219 6.04368 20.0219 6.04368V9.39008H18.3166C16.6383 9.39008 16.1144 10.4396 16.1144 11.5162V14.0683H19.863L19.2647 17.9995H16.1127V27.5063C22.5756 26.4855 27.5209 20.8574 27.5209 14.0683Z"
                fill="#29C16E"
              />
            </g>
            <defs>
              <clipPath id="clip0_119_2775">
                <rect
                  x="0.479248"
                  y="0.464661"
                  width="27.0417"
                  height="27.0417"
                  rx="13.5208"
                  fill="white"
                />
              </clipPath>
            </defs>
          </svg>

          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_119_2773)">
              <rect
                x="0.182373"
                y="0.464661"
                width="27.0417"
                height="27.0417"
                rx="13.5208"
                fill="white"
              />
              <path
                d="M13.7033 0.464663C10.5507 0.462664 7.49655 1.56235 5.06887 3.57357C2.6412 5.58479 0.992625 8.3811 0.408207 11.479C-0.17621 14.5769 0.340267 17.7817 1.86834 20.5391C3.3964 23.2966 5.84 25.4334 8.77661 26.5802C8.6583 25.5103 8.55013 23.8658 8.82224 22.698C9.06899 21.6417 10.4076 15.9764 10.4076 15.9764C10.4076 15.9764 10.0036 15.1669 10.0036 13.9703C10.0036 12.0892 11.0937 10.6864 12.4509 10.6864C13.6035 10.6864 14.1613 11.5517 14.1613 12.5912C14.1613 13.7506 13.4227 15.4846 13.0407 17.0919C12.723 18.4372 13.7168 19.5358 15.0435 19.5358C17.4468 19.5358 19.2941 17.0006 19.2941 13.3433C19.2941 10.1067 16.9669 7.84366 13.6458 7.84366C9.79912 7.84366 7.54114 10.7287 7.54114 13.71C7.54114 14.8728 7.98902 16.1184 8.54675 16.7961C8.59478 16.8472 8.62875 16.9099 8.64534 16.978C8.66192 17.0462 8.66057 17.1174 8.6414 17.1849C8.5383 17.6108 8.31014 18.5302 8.2662 18.7178C8.20704 18.9645 8.07014 19.0169 7.81325 18.8986C6.12314 18.1127 5.06852 15.6435 5.06852 13.6593C5.06852 9.39517 8.16817 5.4792 14.0024 5.4792C18.6924 5.4792 22.338 8.82054 22.338 13.2875C22.338 17.9471 19.3989 21.6974 15.3224 21.6974C13.9517 21.6974 12.6638 20.9859 12.2227 20.1442L11.3811 23.3588C11.0751 24.5334 10.2504 26.0055 9.6994 26.903C11.5585 27.4778 13.5197 27.6445 15.4491 27.3918C17.3786 27.1392 19.2307 26.473 20.8791 25.4388C22.5274 24.4047 23.9331 23.0269 25.0002 21.3997C26.0672 19.7724 26.7704 17.934 27.0618 16.01C27.3532 14.0861 27.2258 12.1219 26.6884 10.2516C26.1511 8.38141 25.2163 6.6492 23.9481 5.17335C22.6799 3.69751 21.108 2.5128 19.3399 1.70015C17.5718 0.887511 15.6492 0.466084 13.7033 0.464663Z"
                fill="#29C16E"
              />
            </g>
            <defs>
              <clipPath id="clip0_119_2773">
                <rect
                  x="0.182373"
                  y="0.464661"
                  width="27.0417"
                  height="27.0417"
                  rx="13.5208"
                  fill="white"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
      </div>
    </div>
  );
}
