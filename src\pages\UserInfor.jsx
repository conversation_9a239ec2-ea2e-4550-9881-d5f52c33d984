import React from "react";
import ImgAvatar from "../assets/img/image.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faRectangleList,
  faUser,
  faAddressBook,
  faCreditCard,
} from "@fortawesome/free-regular-svg-icons";
import { faGift } from "@fortawesome/free-solid-svg-icons";
import AccountDetails from "../components/UserInfor/AccountDetails";
import { Link, Outlet } from "react-router";
export default function UserInfor() {
  const user = JSON.parse(localStorage.getItem("user"));
  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 ">
      <div className="bg-gray-200 p-4 rounded-xl shadow-md col-span-1  ">
        <div className=" flex flex-row gap-2 items-center justify-center ">
          <img
            src={ImgAvatar}
            alt="user-icon"
            className="w-18 h-18 rounded-full "
          />
          <div>
            <h1 className="text-xl font-medium">
              {user?.username || "Tên người dùng"}
            </h1>
            <p className="text-sm font-bold text-green-600">
              Vai trò: {user?.role || ""}
            </p>
          </div>
        </div>
        <ul className="text-xl text-gray-500 font-semibold flex flex-col gap-4 mt-4 p-2  ">
          <li className="hover:text-green-600">
            <FontAwesomeIcon
              icon={faUser}
              className="mr-2 text-2xl text-green-600"
            />
            <Link to={"/user"}>Account Details</Link>
          </li>
          <li className="hover:text-green-600 ">
            <FontAwesomeIcon
              icon={faRectangleList}
              className="mr-2 text-2xl text-green-600"
            />
            <Link to={"/user/order"}>My Orders</Link>
          </li>
          <li className="hover:text-green-600">
            <FontAwesomeIcon
              icon={faAddressBook}
              className="mr-2 text-2xl text-green-600"
            />
            <Link to={"/user/address"}>My Address</Link>
          </li>
          <li className="hover:text-green-600">
            <FontAwesomeIcon
              icon={faCreditCard}
              className="mr-2 text-2xl text-green-600"
            />
            <Link to={"/user/payment"}>My Payment</Link>
          </li>
          <li className="hover:text-green-600">
            <FontAwesomeIcon
              icon={faGift}
              className="mr-2 text-2xl text-green-600"
            />
            <Link to={"/user/coupons"}>Coupons</Link>
          </li>
        </ul>
      </div>
      <Outlet />
    </div>
  );
}
