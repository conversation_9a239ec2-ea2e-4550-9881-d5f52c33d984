import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBox } from "@fortawesome/free-solid-svg-icons";
const Category = ({ list, onCategoryClick }) => {
  // Thê<PERSON> danh mục "Tất cả" vào đầu danh sách
  const allCategory = { id: "all", name: "Tất cả" };
  const categories = [allCategory, ...list];
  return (
    <div>
      <h1 className="text-2xl mt-6 mb-6">Category</h1>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-6 mb-6">
        {categories.map((category) => (
          <div
            key={category.id}
            className="flex flex-col items-center bg-white rounded-lg shadow-md p-4 hover:shadow-xl  cursor-pointer hover:scale-105 transition-transform duration-300"
            onClick={() => onCategoryClick && onCategoryClick(category.id)}
          >
            <FontAwesomeIcon
              icon={faBox}
              alt={category.name}
              className="w-32 h-32 object-cover rounded-md mb-3 text-3xl text-green-500"
            />
            <h3 className="text-lg font-semibold text-gray-800">
              {category.name}
            </h3>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Category;
