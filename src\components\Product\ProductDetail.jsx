import Coment from "./Coment";
import ListComent from "./ListComent";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faStar as solidStar } from "@fortawesome/free-solid-svg-icons";
import {
  faStar as regularStar,
  faCartShopping,
} from "@fortawesome/free-solid-svg-icons";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router";

export default function ProductDetail() {
  const [product, setProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [comments, setComments] = useState([
    {
      user: "Nguyễn Văn A",
      content: "Sản phẩm rất tốt!",
      rating: 5,
      date: "2025-07-20",
    },
    {
      user: "Trần Thị B",
      content: "Đóng gói cẩn thận, giao hàng nhanh.",
      rating: 4,
      date: "2025-07-21",
    },
  ]);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    // Lấy thông tin sản phẩm từ localStorage (có thể thay bằng API nếu cần)
    const data = localStorage.getItem("productDetail");
    if (data) {
      setProduct(JSON.parse(data));
    }
  }, [id]);

  // Giả lập tên user đăng nhập
  const userName = "User Demo";

  const handleAddComment = (comment) => {
    setComments([
      { ...comment, date: new Date().toLocaleDateString("vi-VN") },
      ...comments,
    ]);
  };

  const handleAddToCart = () => {
    if (!product) return;
    let cart = JSON.parse(localStorage.getItem("cart")) || [];
    const index = cart.findIndex((item) => item.id === product.id);
    if (index !== -1) {
      cart[index].quantity += quantity;
    } else {
      cart.push({ ...product, quantity });
    }
    localStorage.setItem("cart", JSON.stringify(cart));
    alert("Đã thêm vào giỏ hàng!");
  };

  // Tính điểm trung bình sao
  const avgRating = comments.length
    ? (
        comments.reduce((sum, c) => sum + (c.rating || 0), 0) / comments.length
      ).toFixed(1)
    : 0;

  if (!product) return <div>Đang tải chi tiết sản phẩm...</div>;

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="flex flex-col md:flex-row gap-8 bg-white rounded-xl shadow-md p-6 mb-6">
        <img
          src={product.image}
          alt={product.name}
          className="w-64 h-64 object-contain rounded"
        />
        <div className="flex-1 flex flex-col gap-2">
          <h1 className="text-2xl font-bold mb-1">{product.name}</h1>
          <div className="flex items-center gap-2 mb-2">
            <span className="flex gap-0.5">
              {[1, 2, 3, 4, 5].map((star) => (
                <FontAwesomeIcon
                  key={star}
                  icon={star <= Math.round(avgRating) ? solidStar : regularStar}
                  className={
                    star <= Math.round(avgRating)
                      ? "text-yellow-400"
                      : "text-gray-300"
                  }
                />
              ))}
            </span>
            <span className="text-sm text-gray-600">
              {avgRating} ({comments.length} đánh giá)
            </span>
          </div>
          <div className="flex items-end gap-3 mb-2">
            <span className="text-green-600 font-bold text-xl">
              {product.price.toLocaleString()}₫
            </span>
            {product.originalPrice && (
              <span className="text-gray-400 line-through text-base">
                {product.originalPrice.toLocaleString()}₫
              </span>
            )}
          </div>
          <div className="mb-2 text-gray-700">{product.description}</div>
          <div className="flex items-center gap-2 mb-4">
            <span>Số lượng:</span>
            <button
              className="px-2 py-1 bg-gray-200 rounded"
              onClick={() => setQuantity((q) => (q > 1 ? q - 1 : 1))}
            >
              -
            </button>
            <input
              type="number"
              min={1}
              value={quantity}
              onChange={(e) => setQuantity(Math.max(1, Number(e.target.value)))}
              className="w-16 text-center border rounded"
            />
            <button
              className="px-2 py-1 bg-gray-200 rounded"
              onClick={() => setQuantity((q) => q + 1)}
            >
              +
            </button>
          </div>
          <button
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded w-max flex items-center gap-2"
            onClick={handleAddToCart}
          >
            <FontAwesomeIcon icon={faCartShopping} /> Thêm vào giỏ hàng
          </button>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Bình luận sản phẩm</h2>
        <Coment onAdd={handleAddComment} userName={userName} />
        <ListComent comments={comments} />
      </div>
    </div>
  );
}
