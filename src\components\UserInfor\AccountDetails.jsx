import React from "react";
import { decodeJWT } from "../../utils/auth";

export default function AccountDetails() {
  // L<PERSON>y thông tin user từ JWT
  const token = localStorage.getItem("token");
  const decoded = decodeJWT(token) || {};
  const details = {
    username: decoded.username || decoded.name || "",
    email: decoded.email || "",
    phone: decoded.phone || "",
    address: decoded.address || "",
    role: decoded.role || "",
  };

  return (
    <div className="col-span-4">
      <h1 className="text-2xl  md:text-3xl font-semibold m-4">
        Account Details
      </h1>
      <div className="rounded-xl m-4 p-4 shadow-md flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <p className="font-semibold text-xl">Username</p>
          <p className="text-lg">{details.username}</p>
        </div>
        <div className="flex flex-col gap-2  rounded-2xl">
          <p className="font-semibold text-xl">Email Address</p>
          <p className="text-lg">{details.email}</p>
        </div>
        <div className="flex flex-col gap-2">
          <p className="font-semibold text-xl">Mobile Number</p>
          <p className="text-lg">{details.phone}</p>
        </div>
        <div className="flex flex-col gap-2">
          <p className="font-semibold text-xl">Address</p>
          <p className="text-lg">{details.address}</p>
        </div>
        <div className="flex flex-col gap-2">
          <p className="font-semibold text-xl">Role</p>
          <p className="text-lg">{details.role}</p>
        </div>
      </div>
    </div>
  );
}
