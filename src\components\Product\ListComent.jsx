import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faStar as solidStar } from "@fortawesome/free-solid-svg-icons";
import { faStar as regularStar } from "@fortawesome/free-regular-svg-icons";

export default function ListComent({ comments }) {
  if (!comments || comments.length === 0)
    return <div className="text-gray-500">Chưa có bình luận nào.</div>;
  return (
    <div className="space-y-4">
      {comments.map((c, idx) => (
        <div key={idx} className="border-b pb-3">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-semibold text-blue-600">{c.user}</span>
            <span className="flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <FontAwesomeIcon
                  key={star}
                  icon={star <= (c.rating || 0) ? solidStar : regularStar}
                  className={
                    star <= (c.rating || 0)
                      ? "text-yellow-400 text-sm"
                      : "text-gray-400 text-sm"
                  }
                />
              ))}
            </span>
            <span className="text-xs text-gray-400">{c.date}</span>
          </div>
          <div className="text-gray-800">{c.content}</div>
        </div>
      ))}
    </div>
  );
}
