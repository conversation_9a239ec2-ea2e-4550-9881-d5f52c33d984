import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faStar as solidStar } from "@fortawesome/free-solid-svg-icons";
import { faStar as regularStar } from "@fortawesome/free-regular-svg-icons";

export default function Coment({ onAdd, userName }) {
  const [content, setContent] = useState("");
  const [rating, setRating] = useState(0);
  const [error, setError] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!content.trim() || rating === 0) {
      setError("Vui lòng nhập nội dung và chọn số sao.");
      return;
    }
    onAdd({ user: userName, content, rating });
    setContent("");
    setRating(0);
    setError("");
  };

  return (
    <form onSubmit={handleSubmit} className="mb-6 flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <span className="font-medium">Đánh giá:</span>
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            type="button"
            key={star}
            onClick={() => setRating(star)}
            className="focus:outline-none"
          >
            <FontAwesomeIcon
              icon={star <= rating ? solidStar : regularStar}
              className={
                star <= rating
                  ? "text-yellow-400 text-xl"
                  : "text-gray-400 text-xl"
              }
            />
          </button>
        ))}
      </div>
      <textarea
        placeholder="Nội dung bình luận..."
        className="border rounded px-3 py-2"
        value={content}
        onChange={(e) => setContent(e.target.value)}
        rows={3}
      />
      {error && <div className="text-red-500 text-sm">{error}</div>}
      <button
        type="submit"
        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded w-max"
      >
        Gửi bình luận
      </button>
    </form>
  );
}
